"""
Comparison between merge and delete+append approaches for file replacement.
"""

import os
import shutil
import time
from deltalake import DeltaTable, write_deltalake
from .data_generator import (
    create_initial_table_data,
    create_file_replacement_same_rows,
    create_large_file_data,
    print_table_summary
)


class MergeVsDeleteAppendComparison:
    """Compare merge vs delete+append approaches for file replacement."""
    
    def __init__(self, table_path: str = "./comparison_table"):
        self.table_path = table_path
    
    def setup_table(self):
        """Create initial Delta table with sample data."""
        if os.path.exists(self.table_path):
            shutil.rmtree(self.table_path)
        
        initial_data = create_initial_table_data()
        write_deltalake(self.table_path, initial_data, mode="overwrite")
        return DeltaTable(self.table_path)
    
    def approach_1_merge(self, new_data, file_id: str):
        """Approach 1: Use merge operation for file replacement."""
        dt = DeltaTable(self.table_path)
        
        start_time = time.time()
        
        result = dt.merge(
            source=new_data,
            predicate="false",  # Dummy join - never matches
            source_alias="source",
            target_alias="target"
        ).when_not_matched_by_source_delete(
            predicate=f"target.file_id = '{file_id}'"
        ).when_not_matched_insert(
            {
                "file_id": "source.file_id",
                "customer_id": "source.customer_id",
                "transaction_id": "source.transaction_id",
                "amount": "source.amount",
                "transaction_date": "source.transaction_date",
                "processed_at": "source.processed_at"
            }
        ).execute()
        
        execution_time = time.time() - start_time
        
        return {
            "approach": "merge",
            "execution_time": execution_time,
            "metrics": result,
            "final_version": dt.version()
        }
    
    def approach_2_delete_append(self, new_data, file_id: str):
        """Approach 2: Use delete + append for file replacement."""
        dt = DeltaTable(self.table_path)
        
        start_time = time.time()
        
        # Step 1: Delete existing records for the file
        delete_start = time.time()
        delete_result = dt.delete(predicate=f"file_id = '{file_id}'")
        delete_time = time.time() - delete_start
        
        # Step 2: Append new records
        append_start = time.time()
        write_deltalake(self.table_path, new_data, mode="append")
        append_time = time.time() - append_start
        
        total_time = time.time() - start_time
        
        # Get final table state
        dt_final = DeltaTable(self.table_path)
        
        return {
            "approach": "delete+append",
            "execution_time": total_time,
            "delete_time": delete_time,
            "append_time": append_time,
            "delete_metrics": delete_result,
            "final_version": dt_final.version()
        }
    
    def compare_small_data(self):
        """Compare approaches with small dataset."""
        print("\n" + "="*80)
        print("COMPARISON: Small Dataset (file_A: 3 → 3 rows)")
        print("="*80)
        
        new_data = create_file_replacement_same_rows()
        print_table_summary(new_data, "Replacement Data")
        
        # Test merge approach
        print("\n🔄 Testing MERGE approach...")
        self.setup_table()
        merge_result = self.approach_1_merge(new_data, "file_A")
        
        # Test delete+append approach  
        print("\n🔄 Testing DELETE+APPEND approach...")
        self.setup_table()
        delete_append_result = self.approach_2_delete_append(new_data, "file_A")
        
        # Compare results
        self.print_comparison(merge_result, delete_append_result)
    
    def compare_large_data(self):
        """Compare approaches with large dataset."""
        print("\n" + "="*80)
        print("COMPARISON: Large Dataset (file_A: 3 → 10,000 rows)")
        print("="*80)
        
        # Create large replacement data
        new_data = create_large_file_data("file_A", 10000)
        print_table_summary(new_data, "Large Replacement Data")
        
        # Test merge approach
        print("\n🔄 Testing MERGE approach...")
        self.setup_table()
        merge_result = self.approach_1_merge(new_data, "file_A")
        
        # Test delete+append approach
        print("\n🔄 Testing DELETE+APPEND approach...")
        self.setup_table()
        delete_append_result = self.approach_2_delete_append(new_data, "file_A")
        
        # Compare results
        self.print_comparison(merge_result, delete_append_result)
    
    def compare_very_large_data(self):
        """Compare approaches with very large dataset."""
        print("\n" + "="*80)
        print("COMPARISON: Very Large Dataset (file_A: 3 → 100,000 rows)")
        print("="*80)
        
        # Create very large replacement data
        new_data = create_large_file_data("file_A", 100000)
        print_table_summary(new_data, "Very Large Replacement Data")
        
        # Test merge approach
        print("\n🔄 Testing MERGE approach...")
        self.setup_table()
        merge_result = self.approach_1_merge(new_data, "file_A")
        
        # Test delete+append approach
        print("\n🔄 Testing DELETE+APPEND approach...")
        self.setup_table()
        delete_append_result = self.approach_2_delete_append(new_data, "file_A")
        
        # Compare results
        self.print_comparison(merge_result, delete_append_result)
    
    def print_comparison(self, merge_result, delete_append_result):
        """Print detailed comparison of results."""
        print("\n📊 PERFORMANCE COMPARISON")
        print("-" * 50)
        
        print(f"Merge approach:")
        print(f"  ⏱️  Total time: {merge_result['execution_time']:.3f}s")
        print(f"  📝 Final version: {merge_result['final_version']}")
        print(f"  📈 Metrics: {merge_result['metrics']}")
        
        print(f"\nDelete+Append approach:")
        print(f"  ⏱️  Total time: {delete_append_result['execution_time']:.3f}s")
        print(f"  ⏱️  Delete time: {delete_append_result['delete_time']:.3f}s")
        print(f"  ⏱️  Append time: {delete_append_result['append_time']:.3f}s")
        print(f"  📝 Final version: {delete_append_result['final_version']}")
        print(f"  📈 Delete metrics: {delete_append_result['delete_metrics']}")
        
        # Performance comparison
        speedup = delete_append_result['execution_time'] / merge_result['execution_time']
        if speedup > 1:
            print(f"\n🏆 Merge is {speedup:.2f}x FASTER than delete+append")
        else:
            print(f"\n🏆 Delete+append is {1/speedup:.2f}x FASTER than merge")
        
        # Transaction comparison
        merge_transactions = 1  # Single transaction
        delete_append_transactions = 2  # Delete + append
        
        print(f"\n🔄 TRANSACTION COMPARISON")
        print(f"  Merge: {merge_transactions} transaction (atomic)")
        print(f"  Delete+append: {delete_append_transactions} transactions (risk of partial failure)")
        
        # Version comparison
        print(f"\n📝 VERSION COMPARISON")
        print(f"  Merge: Creates 1 new version")
        print(f"  Delete+append: Creates 2 new versions")
    
    def cleanup(self):
        """Clean up the comparison table."""
        if os.path.exists(self.table_path):
            shutil.rmtree(self.table_path)
            print(f"\n🧹 Cleaned up table at: {self.table_path}")


def run_comparison():
    """Run all comparison tests."""
    print("🚀 Starting Merge vs Delete+Append Comparison")
    print("="*80)
    
    comparison = MergeVsDeleteAppendComparison()
    
    try:
        comparison.compare_small_data()
        comparison.compare_large_data()
        comparison.compare_very_large_data()
        
        print("\n" + "="*80)
        print("📋 SUMMARY")
        print("="*80)
        print("✅ Merge approach:")
        print("  + Single atomic transaction")
        print("  + Better consistency guarantees")
        print("  + Single table version increment")
        print("  - Slightly more complex syntax")
        
        print("\n✅ Delete+append approach:")
        print("  + Simpler conceptual model")
        print("  + More explicit operations")
        print("  - Two separate transactions")
        print("  - Risk of partial failure")
        print("  - Creates two table versions")
        
        print("\n🎯 RECOMMENDATION:")
        print("For file replacement scenarios, MERGE is generally preferred due to:")
        print("- Atomic operations (better consistency)")
        print("- Single transaction (better concurrency)")
        print("- Cleaner version history")
        
    finally:
        comparison.cleanup()


if __name__ == "__main__":
    run_comparison()
