"""
ACID transaction analysis for Delta Lake file replacement approaches.
Demonstrates merge vs delete+append with Delta log inspection.
"""

import os
import shutil
import json
import time
from pathlib import Path
from deltalake import DeltaTable, write_deltalake
import pyarrow as pa
import pandas as pd
from .data_generator import create_initial_table_data, create_file_replacement_same_rows


class ACIDAnalysis:
    """Analyze ACID properties and Delta log behavior for file replacement approaches."""
    
    def __init__(self, table_path: str = "./acid_test_table"):
        self.table_path = table_path
        self.delta_log_path = Path(table_path) / "_delta_log"
    
    def setup_table(self):
        """Create initial Delta table."""
        if os.path.exists(self.table_path):
            shutil.rmtree(self.table_path)
        
        initial_data = create_initial_table_data()
        write_deltalake(self.table_path, initial_data, mode="overwrite")
        
        print(f"✅ Created Delta table at: {self.table_path}")
        self.inspect_delta_log("Initial table creation")
        return DeltaTable(self.table_path)
    
    def inspect_delta_log(self, operation_name: str):
        """Inspect Delta log files and show transaction details."""
        print(f"\n🔍 Delta Log Analysis: {operation_name}")
        print("-" * 60)
        
        if not self.delta_log_path.exists():
            print("❌ Delta log directory not found")
            return
        
        # List all log files
        log_files = sorted(self.delta_log_path.glob("*.json"))
        print(f"📁 Log files: {len(log_files)} files")
        
        for log_file in log_files:
            print(f"\n📄 {log_file.name}:")
            try:
                with open(log_file, 'r') as f:
                    for line_num, line in enumerate(f, 1):
                        if line.strip():
                            entry = json.loads(line.strip())
                            self.analyze_log_entry(entry, line_num)
            except Exception as e:
                print(f"❌ Error reading {log_file.name}: {e}")
    
    def analyze_log_entry(self, entry: dict, line_num: int):
        """Analyze a single Delta log entry."""
        if "commitInfo" in entry:
            commit_info = entry["commitInfo"]
            print(f"  Line {line_num}: 📝 COMMIT")
            print(f"    ⏰ Timestamp: {commit_info.get('timestamp', 'N/A')}")
            print(f"    👤 Operation: {commit_info.get('operation', 'N/A')}")
            print(f"    📊 Version: {commit_info.get('version', 'N/A')}")
            if 'operationMetrics' in commit_info:
                metrics = commit_info['operationMetrics']
                print(f"    📈 Metrics: {metrics}")
        
        elif "add" in entry:
            add_info = entry["add"]
            print(f"  Line {line_num}: ➕ ADD FILE")
            print(f"    📁 Path: {add_info['path']}")
            print(f"    📏 Size: {add_info.get('size', 'N/A')} bytes")
            if 'stats' in add_info:
                stats = json.loads(add_info['stats']) if isinstance(add_info['stats'], str) else add_info['stats']
                print(f"    📊 Rows: {stats.get('numRecords', 'N/A')}")
        
        elif "remove" in entry:
            remove_info = entry["remove"]
            print(f"  Line {line_num}: ❌ REMOVE FILE")
            print(f"    📁 Path: {remove_info['path']}")
            print(f"    ⏰ Deletion timestamp: {remove_info.get('deletionTimestamp', 'N/A')}")
        
        elif "metaData" in entry:
            print(f"  Line {line_num}: 🏗️  METADATA")
            metadata = entry["metaData"]
            print(f"    🆔 ID: {metadata.get('id', 'N/A')}")
            print(f"    📋 Schema fields: {len(metadata.get('schema', {}).get('fields', []))}")
    
    def approach_1_merge_with_dummy_join(self):
        """Approach 1: Merge with dummy join (never matches)."""
        print("\n" + "="*80)
        print("APPROACH 1: MERGE WITH DUMMY JOIN")
        print("="*80)
        
        dt = self.setup_table()
        initial_version = dt.version()
        
        # Show initial state
        print(f"\n📊 Initial table version: {initial_version}")
        print(f"📊 Initial row count: {dt.to_pyarrow_table().num_rows}")
        
        # Create replacement data
        new_data = create_file_replacement_same_rows()
        print(f"📊 Replacement data rows: {new_data.num_rows}")
        
        # Perform merge operation
        print("\n🔄 Executing merge operation...")
        start_time = time.time()
        
        try:
            result = dt.merge(
                source=new_data,
                predicate="false",  # Dummy join - never matches
                source_alias="source",
                target_alias="target"
            ).when_not_matched_by_source_delete(
                predicate="target.file_id = 'file_A'"
            ).when_not_matched_insert(
                {
                    "file_id": "source.file_id",
                    "customer_id": "source.customer_id",
                    "transaction_id": "source.transaction_id",
                    "amount": "source.amount",
                    "transaction_date": "source.transaction_date",
                    "processed_at": "source.processed_at"
                }
            ).execute()
            
            execution_time = time.time() - start_time
            
            # Get final state
            dt_final = DeltaTable(self.table_path)
            final_version = dt_final.version()
            final_rows = dt_final.to_pyarrow_table().num_rows
            
            print(f"✅ Merge completed successfully!")
            print(f"⏱️  Execution time: {execution_time:.3f} seconds")
            print(f"📊 Final version: {final_version} (increment: {final_version - initial_version})")
            print(f"📊 Final row count: {final_rows}")
            print(f"📈 Merge metrics: {result}")
            
            # Inspect Delta log
            self.inspect_delta_log("Merge operation")
            
            return {
                "success": True,
                "execution_time": execution_time,
                "version_increment": final_version - initial_version,
                "final_rows": final_rows,
                "metrics": result
            }
            
        except Exception as e:
            print(f"❌ Merge operation failed: {e}")
            return {"success": False, "error": str(e)}
    
    def approach_2_delete_then_append(self):
        """Approach 2: Delete + Append (two separate transactions)."""
        print("\n" + "="*80)
        print("APPROACH 2: DELETE + APPEND")
        print("="*80)
        
        dt = self.setup_table()
        initial_version = dt.version()
        
        # Show initial state
        print(f"\n📊 Initial table version: {initial_version}")
        print(f"📊 Initial row count: {dt.to_pyarrow_table().num_rows}")
        
        # Create replacement data
        new_data = create_file_replacement_same_rows()
        print(f"📊 Replacement data rows: {new_data.num_rows}")
        
        print("\n🔄 Executing delete + append operations...")
        total_start_time = time.time()
        
        try:
            # Step 1: Delete existing records
            print("\n  Step 1: Deleting existing records for file_A...")
            delete_start = time.time()
            
            delete_result = dt.delete(predicate="file_id = 'file_A'")
            delete_time = time.time() - delete_start
            
            # Check state after delete
            dt_after_delete = DeltaTable(self.table_path)
            version_after_delete = dt_after_delete.version()
            rows_after_delete = dt_after_delete.to_pyarrow_table().num_rows
            
            print(f"  ✅ Delete completed")
            print(f"  ⏱️  Delete time: {delete_time:.3f} seconds")
            print(f"  📊 Version after delete: {version_after_delete}")
            print(f"  📊 Rows after delete: {rows_after_delete}")
            print(f"  📈 Delete metrics: {delete_result}")
            
            # Inspect Delta log after delete
            self.inspect_delta_log("After delete operation")
            
            # Step 2: Append new records
            print("\n  Step 2: Appending new records...")
            append_start = time.time()
            
            write_deltalake(self.table_path, new_data, mode="append")
            append_time = time.time() - append_start
            
            # Check final state
            dt_final = DeltaTable(self.table_path)
            final_version = dt_final.version()
            final_rows = dt_final.to_pyarrow_table().num_rows
            
            total_time = time.time() - total_start_time
            
            print(f"  ✅ Append completed")
            print(f"  ⏱️  Append time: {append_time:.3f} seconds")
            print(f"  📊 Final version: {final_version}")
            print(f"  📊 Final rows: {final_rows}")
            
            print(f"\n✅ Delete + Append completed successfully!")
            print(f"⏱️  Total execution time: {total_time:.3f} seconds")
            print(f"📊 Total version increment: {final_version - initial_version}")
            
            # Inspect final Delta log
            self.inspect_delta_log("After append operation")
            
            return {
                "success": True,
                "total_time": total_time,
                "delete_time": delete_time,
                "append_time": append_time,
                "version_increment": final_version - initial_version,
                "final_rows": final_rows,
                "delete_metrics": delete_result
            }
            
        except Exception as e:
            print(f"❌ Delete + Append operation failed: {e}")
            return {"success": False, "error": str(e)}
    
    def analyze_acid_properties(self, merge_result: dict, delete_append_result: dict):
        """Analyze ACID properties of both approaches."""
        print("\n" + "="*80)
        print("ACID PROPERTIES ANALYSIS")
        print("="*80)
        
        print("\n🔒 ATOMICITY:")
        print("  Merge approach:")
        if merge_result.get("success"):
            print("    ✅ Single transaction - all operations succeed or fail together")
            print(f"    📊 Version increment: {merge_result['version_increment']} (atomic)")
        else:
            print("    ❌ Operation failed atomically")
        
        print("  Delete+Append approach:")
        if delete_append_result.get("success"):
            print("    ⚠️  Two separate transactions - risk of partial failure")
            print(f"    📊 Version increment: {delete_append_result['version_increment']} (non-atomic)")
            print("    🚨 If append fails after delete, data could be lost!")
        else:
            print("    ❌ Operation failed (potentially partial)")
        
        print("\n🔄 CONSISTENCY:")
        print("  Merge approach:")
        print("    ✅ Maintains referential integrity throughout operation")
        print("    ✅ No intermediate inconsistent states visible to readers")
        
        print("  Delete+Append approach:")
        print("    ⚠️  Intermediate state exists where file_A data is missing")
        print("    ⚠️  Concurrent readers might see incomplete data between operations")
        
        print("\n🔐 ISOLATION:")
        print("  Merge approach:")
        print("    ✅ Single transaction provides better isolation")
        print("    ✅ Concurrent operations less likely to conflict")
        
        print("  Delete+Append approach:")
        print("    ⚠️  Two transactions increase chance of conflicts")
        print("    ⚠️  Other operations might interfere between delete and append")
        
        print("\n💾 DURABILITY:")
        print("  Both approaches:")
        print("    ✅ Delta Lake ensures durability through transaction log")
        print("    ✅ Operations are durable once committed")
    
    def performance_analysis(self, merge_result: dict, delete_append_result: dict):
        """Analyze performance characteristics."""
        print("\n" + "="*80)
        print("PERFORMANCE ANALYSIS")
        print("="*80)
        
        if merge_result.get("success") and delete_append_result.get("success"):
            merge_time = merge_result["execution_time"]
            delete_append_time = delete_append_result["total_time"]
            
            print(f"\n⏱️  EXECUTION TIME:")
            print(f"  Merge: {merge_time:.3f}s")
            print(f"  Delete+Append: {delete_append_time:.3f}s")
            
            if merge_time < delete_append_time:
                speedup = delete_append_time / merge_time
                print(f"  🏆 Merge is {speedup:.2f}x faster")
            else:
                speedup = merge_time / delete_append_time
                print(f"  🏆 Delete+Append is {speedup:.2f}x faster")
            
            print(f"\n📊 VERSION MANAGEMENT:")
            print(f"  Merge: {merge_result['version_increment']} version(s)")
            print(f"  Delete+Append: {delete_append_result['version_increment']} version(s)")
            print(f"  💡 Fewer versions = cleaner history and better performance")
            
            print(f"\n🔄 TRANSACTION OVERHEAD:")
            print(f"  Merge: 1 transaction")
            print(f"  Delete+Append: 2 transactions")
            print(f"  💡 Fewer transactions = less coordination overhead")
    
    def cleanup(self):
        """Clean up test table."""
        if os.path.exists(self.table_path):
            shutil.rmtree(self.table_path)
            print(f"\n🧹 Cleaned up table at: {self.table_path}")


def run_acid_analysis():
    """Run complete ACID and performance analysis."""
    print("🚀 Starting ACID Transaction Analysis")
    print("="*80)
    
    analyzer = ACIDAnalysis()
    
    try:
        # Test both approaches
        merge_result = analyzer.approach_1_merge_with_dummy_join()
        delete_append_result = analyzer.approach_2_delete_then_append()
        
        # Analyze results
        analyzer.analyze_acid_properties(merge_result, delete_append_result)
        analyzer.performance_analysis(merge_result, delete_append_result)
        
        print("\n" + "="*80)
        print("🎯 RECOMMENDATIONS")
        print("="*80)
        print("For file replacement in production ETL:")
        print("✅ USE MERGE when:")
        print("  - ACID guarantees are critical")
        print("  - Concurrent operations are common")
        print("  - Clean version history is important")
        print("  - Data consistency is paramount")
        
        print("\n✅ USE DELETE+APPEND when:")
        print("  - Operations are serialized (no concurrency)")
        print("  - Simplicity is more important than atomicity")
        print("  - You have robust error handling/retry logic")
        print("  - Performance is not critical")
        
    finally:
        analyzer.cleanup()


if __name__ == "__main__":
    run_acid_analysis()
