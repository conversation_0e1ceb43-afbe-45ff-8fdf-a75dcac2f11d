"""
Data generation utilities for Delta Lake merge examples.
"""

import pandas as pd
import pyarrow as pa
from datetime import datetime, timedelta
from typing import List, Dict, Any
import random


def create_initial_table_data() -> pa.Table:
    """Create initial table data with transactions from multiple files."""
    data = [
        # file_A data (3 rows)
        {"file_id": "file_A", "customer_id": "cust_001", "transaction_id": "txn_001", 
         "amount": 100.00, "transaction_date": "2024-01-01", "processed_at": datetime.now()},
        {"file_id": "file_A", "customer_id": "cust_002", "transaction_id": "txn_002", 
         "amount": 250.00, "transaction_date": "2024-01-01", "processed_at": datetime.now()},
        {"file_id": "file_A", "customer_id": "cust_003", "transaction_id": "txn_003", 
         "amount": 75.50, "transaction_date": "2024-01-02", "processed_at": datetime.now()},
        
        # file_B data (2 rows)
        {"file_id": "file_B", "customer_id": "cust_004", "transaction_id": "txn_004", 
         "amount": 300.00, "transaction_date": "2024-01-01", "processed_at": datetime.now()},
        {"file_id": "file_B", "customer_id": "cust_005", "transaction_id": "txn_005", 
         "amount": 150.00, "transaction_date": "2024-01-02", "processed_at": datetime.now()},
        
        # file_C data (1 row)
        {"file_id": "file_C", "customer_id": "cust_006", "transaction_id": "txn_006", 
         "amount": 200.00, "transaction_date": "2024-01-01", "processed_at": datetime.now()},
    ]
    
    df = pd.DataFrame(data)
    return pa.Table.from_pandas(df)


def create_file_replacement_same_rows() -> pa.Table:
    """Create new version of file_A with same number of rows (3) but different data."""
    data = [
        {"file_id": "file_A", "customer_id": "cust_001", "transaction_id": "txn_001", 
         "amount": 120.00, "transaction_date": "2024-01-01", "processed_at": datetime.now()},  # Updated amount
        {"file_id": "file_A", "customer_id": "cust_002", "transaction_id": "txn_007", 
         "amount": 280.00, "transaction_date": "2024-01-03", "processed_at": datetime.now()},  # New transaction
        {"file_id": "file_A", "customer_id": "cust_007", "transaction_id": "txn_008", 
         "amount": 95.00, "transaction_date": "2024-01-03", "processed_at": datetime.now()},   # New customer
    ]
    
    df = pd.DataFrame(data)
    return pa.Table.from_pandas(df)


def create_file_replacement_fewer_rows() -> pa.Table:
    """Create new version of file_A with fewer rows (1 instead of 3)."""
    data = [
        {"file_id": "file_A", "customer_id": "cust_001", "transaction_id": "txn_001", 
         "amount": 150.00, "transaction_date": "2024-01-01", "processed_at": datetime.now()},
    ]
    
    df = pd.DataFrame(data)
    return pa.Table.from_pandas(df)


def create_file_replacement_more_rows() -> pa.Table:
    """Create new version of file_A with more rows (6 instead of 3)."""
    data = [
        {"file_id": "file_A", "customer_id": "cust_001", "transaction_id": "txn_001", 
         "amount": 100.00, "transaction_date": "2024-01-01", "processed_at": datetime.now()},
        {"file_id": "file_A", "customer_id": "cust_002", "transaction_id": "txn_002", 
         "amount": 250.00, "transaction_date": "2024-01-01", "processed_at": datetime.now()},
        {"file_id": "file_A", "customer_id": "cust_003", "transaction_id": "txn_003", 
         "amount": 75.50, "transaction_date": "2024-01-02", "processed_at": datetime.now()},
        {"file_id": "file_A", "customer_id": "cust_008", "transaction_id": "txn_009", 
         "amount": 400.00, "transaction_date": "2024-01-03", "processed_at": datetime.now()},
        {"file_id": "file_A", "customer_id": "cust_009", "transaction_id": "txn_010", 
         "amount": 125.00, "transaction_date": "2024-01-03", "processed_at": datetime.now()},
        {"file_id": "file_A", "customer_id": "cust_010", "transaction_id": "txn_011", 
         "amount": 350.00, "transaction_date": "2024-01-04", "processed_at": datetime.now()},
    ]
    
    df = pd.DataFrame(data)
    return pa.Table.from_pandas(df)


def create_empty_file_replacement() -> pa.Table:
    """Create empty replacement for file_A (0 rows)."""
    # Create empty table with correct schema
    schema = pa.schema([
        pa.field("file_id", pa.string()),
        pa.field("customer_id", pa.string()),
        pa.field("transaction_id", pa.string()),
        pa.field("amount", pa.float64()),
        pa.field("transaction_date", pa.string()),
        pa.field("processed_at", pa.timestamp('us'))
    ])
    
    return pa.Table.from_arrays(
        [pa.array([], type=field.type) for field in schema],
        schema=schema
    )


def create_large_file_data(file_id: str, num_rows: int) -> pa.Table:
    """Create large file data for testing data skew scenarios."""
    data = []
    base_date = datetime(2024, 1, 1)
    
    for i in range(num_rows):
        data.append({
            "file_id": file_id,
            "customer_id": f"cust_{i+1:06d}",
            "transaction_id": f"txn_{i+1:06d}",
            "amount": round(random.uniform(10.0, 1000.0), 2),
            "transaction_date": (base_date + timedelta(days=random.randint(0, 30))).strftime("%Y-%m-%d"),
            "processed_at": datetime.now()
        })
    
    df = pd.DataFrame(data)
    return pa.Table.from_pandas(df)


def print_table_summary(table: pa.Table, title: str):
    """Print a summary of the table contents."""
    print(f"\n{title}")
    print("=" * len(title))
    
    if table.num_rows == 0:
        print("Table is empty (0 rows)")
        return
    
    df = table.to_pandas()
    print(f"Total rows: {len(df)}")
    
    # Group by file_id to show distribution
    file_counts = df.groupby('file_id').size()
    print("\nRows per file:")
    for file_id, count in file_counts.items():
        print(f"  {file_id}: {count} rows")
    
    # Show first few rows
    print(f"\nFirst {min(10, len(df))} rows:")
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', None)
    print(df.head(10).to_string(index=False))


def get_table_schema() -> pa.Schema:
    """Get the standard schema for our transaction table."""
    return pa.schema([
        pa.field("file_id", pa.string()),
        pa.field("customer_id", pa.string()),
        pa.field("transaction_id", pa.string()),
        pa.field("amount", pa.float64()),
        pa.field("transaction_date", pa.string()),
        pa.field("processed_at", pa.timestamp('us'))
    ])
