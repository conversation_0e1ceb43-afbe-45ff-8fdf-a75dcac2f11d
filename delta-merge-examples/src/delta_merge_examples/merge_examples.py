"""
Delta Lake merge operation examples for file replacement scenarios.
"""

import os
import shutil
import time
from pathlib import Path
from deltalake import DeltaTable, write_deltalake
import pyarrow as pa
from .data_generator import (
    create_initial_table_data, 
    create_file_replacement_same_rows,
    create_file_replacement_fewer_rows,
    create_file_replacement_more_rows,
    create_empty_file_replacement,
    print_table_summary
)


class DeltaMergeExamples:
    """Examples demonstrating Delta Lake merge operations for file replacement."""
    
    def __init__(self, table_path: str = "./customer_transactions"):
        self.table_path = table_path
        self.setup_table()
    
    def setup_table(self):
        """Create initial Delta table with sample data."""
        # Clean up existing table
        if os.path.exists(self.table_path):
            shutil.rmtree(self.table_path)
        
        # Create initial data
        initial_data = create_initial_table_data()
        print_table_summary(initial_data, "Initial Table Data")
        
        # Write to Delta table
        write_deltalake(self.table_path, initial_data, mode="overwrite")
        print(f"\n✅ Created Delta table at: {self.table_path}")
    
    def show_current_table(self, title: str = "Current Table State"):
        """Display current table contents."""
        dt = DeltaTable(self.table_path)
        current_data = dt.to_pyarrow_table()
        print_table_summary(current_data, title)
        return dt
    
    def example_1_same_number_of_rows(self):
        """Example 1: Replace file_A with same number of rows (3 → 3)."""
        print("\n" + "="*80)
        print("EXAMPLE 1: Same Number of Rows (3 → 3)")
        print("="*80)
        
        dt = self.show_current_table("Before Merge")
        
        # Create new data for file_A
        new_data = create_file_replacement_same_rows()
        print_table_summary(new_data, "New Data for file_A")
        
        # Perform merge using the "dummy join" approach for complete replacement
        start_time = time.time()
        
        result = dt.merge(
            source=new_data,
            predicate="false",  # Never matches - forces all operations through not_matched branches
            source_alias="source",
            target_alias="target"
        ).when_not_matched_by_source_delete(
            # Delete all existing records for file_A
            predicate="target.file_id = 'file_A'"
        ).when_not_matched_insert(
            # Insert all new records
            {
                "file_id": "source.file_id",
                "customer_id": "source.customer_id", 
                "transaction_id": "source.transaction_id",
                "amount": "source.amount",
                "transaction_date": "source.transaction_date",
                "processed_at": "source.processed_at"
            }
        ).execute()
        
        execution_time = time.time() - start_time
        
        print(f"\n📊 Merge completed in {execution_time:.3f} seconds")
        print(f"📈 Merge metrics: {result}")
        
        self.show_current_table("After Merge - Example 1")
    
    def example_2_fewer_rows(self):
        """Example 2: Replace file_A with fewer rows (3 → 1)."""
        print("\n" + "="*80)
        print("EXAMPLE 2: Fewer Rows (3 → 1)")
        print("="*80)
        
        # Reset table to initial state
        self.setup_table()
        dt = self.show_current_table("Before Merge")
        
        # Create new data for file_A (only 1 row)
        new_data = create_file_replacement_fewer_rows()
        print_table_summary(new_data, "New Data for file_A (1 row)")
        
        # Perform merge
        start_time = time.time()
        
        result = dt.merge(
            source=new_data,
            predicate="false",
            source_alias="source", 
            target_alias="target"
        ).when_not_matched_by_source_delete(
            predicate="target.file_id = 'file_A'"
        ).when_not_matched_insert(
            {
                "file_id": "source.file_id",
                "customer_id": "source.customer_id",
                "transaction_id": "source.transaction_id", 
                "amount": "source.amount",
                "transaction_date": "source.transaction_date",
                "processed_at": "source.processed_at"
            }
        ).execute()
        
        execution_time = time.time() - start_time
        
        print(f"\n📊 Merge completed in {execution_time:.3f} seconds")
        print(f"📈 Merge metrics: {result}")
        
        self.show_current_table("After Merge - Example 2")
    
    def example_3_more_rows(self):
        """Example 3: Replace file_A with more rows (3 → 6)."""
        print("\n" + "="*80)
        print("EXAMPLE 3: More Rows (3 → 6)")
        print("="*80)
        
        # Reset table to initial state
        self.setup_table()
        dt = self.show_current_table("Before Merge")
        
        # Create new data for file_A (6 rows)
        new_data = create_file_replacement_more_rows()
        print_table_summary(new_data, "New Data for file_A (6 rows)")
        
        # Perform merge
        start_time = time.time()
        
        result = dt.merge(
            source=new_data,
            predicate="false",
            source_alias="source",
            target_alias="target"
        ).when_not_matched_by_source_delete(
            predicate="target.file_id = 'file_A'"
        ).when_not_matched_insert(
            {
                "file_id": "source.file_id",
                "customer_id": "source.customer_id",
                "transaction_id": "source.transaction_id",
                "amount": "source.amount", 
                "transaction_date": "source.transaction_date",
                "processed_at": "source.processed_at"
            }
        ).execute()
        
        execution_time = time.time() - start_time
        
        print(f"\n📊 Merge completed in {execution_time:.3f} seconds")
        print(f"📈 Merge metrics: {result}")
        
        self.show_current_table("After Merge - Example 3")
    
    def example_4_empty_file(self):
        """Example 4: Replace file_A with empty file (3 → 0)."""
        print("\n" + "="*80)
        print("EXAMPLE 4: Empty File (3 → 0)")
        print("="*80)
        
        # Reset table to initial state
        self.setup_table()
        dt = self.show_current_table("Before Merge")
        
        # Create empty data for file_A
        new_data = create_empty_file_replacement()
        print_table_summary(new_data, "New Data for file_A (empty)")
        
        # For empty files, we only need to delete existing records
        start_time = time.time()
        
        result = dt.merge(
            source=new_data,
            predicate="false",
            source_alias="source",
            target_alias="target"
        ).when_not_matched_by_source_delete(
            predicate="target.file_id = 'file_A'"
        ).execute()
        
        execution_time = time.time() - start_time
        
        print(f"\n📊 Merge completed in {execution_time:.3f} seconds")
        print(f"📈 Merge metrics: {result}")
        
        self.show_current_table("After Merge - Example 4")
    
    def cleanup(self):
        """Clean up the Delta table."""
        if os.path.exists(self.table_path):
            shutil.rmtree(self.table_path)
            print(f"\n🧹 Cleaned up table at: {self.table_path}")


def run_all_examples():
    """Run all merge examples."""
    print("🚀 Starting Delta Lake Merge Examples")
    print("="*80)
    
    examples = DeltaMergeExamples()
    
    try:
        examples.example_1_same_number_of_rows()
        examples.example_2_fewer_rows() 
        examples.example_3_more_rows()
        examples.example_4_empty_file()
        
        print("\n" + "="*80)
        print("✅ All examples completed successfully!")
        print("="*80)
        
    finally:
        examples.cleanup()


if __name__ == "__main__":
    run_all_examples()
